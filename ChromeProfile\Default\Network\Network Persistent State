{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABQAAABodHRwczovL3ByaW1ldnVlLm9yZw==", false, 0], "broken_count": 1, "host": "rsms.me", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABQAAABodHRwczovL3ByaW1ldnVlLm9yZw==", false, 0], "broken_count": 1, "host": "primevue.org", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "broken_count": 3, "host": "js.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "broken_count": 3, "host": "newassets.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "broken_count": 3, "host": "api.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "broken_count": 2, "host": "js.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "broken_count": 2, "host": "newassets.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "broken_count": 2, "host": "api.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 13, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://clients2.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://wcpstatic.microsoft.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", true, 0], "server": "https://login.microsoftonline.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://www.clarity.ms", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://get.microsoft.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://browser.events.data.microsoft.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://apps.microsoft.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://northcentralus-0.in.applicationinsights.azure.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ogads-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "server": "https://clients2.googleusercontent.com", "supports_spdy": true}, {"anonymization": ["JAAAAB4AAABodHRwczovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20AAA==", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABjaHJvbWU6Ly93aGF0cy1uZXcAAA==", true, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABjaHJvbWU6Ly93aGF0cy1uZXcAAA==", true, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABjaHJvbWU6Ly93aGF0cy1uZXcAAA==", true, 0], "server": "https://csp.withgoogle.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABjaHJvbWU6Ly93aGF0cy1uZXcAAA==", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://encrypted-tbn0.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3ByaW1ldnVlLm9yZw==", false, 0], "server": "https://a.nel.cloudflare.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3ByaW1ldnVlLm9yZw==", false, 0], "server": "https://rsms.me", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3ByaW1ldnVlLm9yZw==", false, 0], "server": "https://www.primefaces.org", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3ByaW1ldnVlLm9yZw==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3ByaW1ldnVlLm9yZw==", false, 0], "server": "https://primevue.org", "supports_spdy": true}, {"anonymization": ["LAAAACUAAABodHRwczovL2Nocm9tZXdlYnN0b3JlLmdvb2dsZWFwaXMuY29tAAAA", false, 0], "server": "https://chromewebstore.googleapis.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3VwZGF0ZS5nb29nbGVhcGlzLmNvbQAAAA==", false, 0], "server": "https://update.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://workspace.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL2dvb2dsZS5jby5pZA==", false, 0], "server": "https://accounts.google.co.id", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://addons-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://mail-ads.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://people-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://signaler-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://contacts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://prod-dynamite-prod-09-us-signaler-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://peoplestack-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["LAAAACgAAABodHRwczovL3NlY3VyaXR5ZG9tYWluLXBhLmdvb2dsZWFwaXMuY29t", false, 0], "server": "https://securitydomain-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL2dtYWlsLmNvbQAAAA==", false, 0], "server": "https://gmail.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://m.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://waa-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://appsgrowthpromo-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://mail.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://chat.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://js.hcaptcha.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://api.hcaptcha.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://newassets.hcaptcha.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://monorail-edge.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://error-analytics-sessions-production.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://shop.app", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://otlp-http-production.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://pay.shopify.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://js.hcaptcha.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://api.hcaptcha.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://error-analytics-sessions-production.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://cdn.shopify.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://a.nel.cloudflare.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://server.shop.app", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://otlp-http-production.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", true, 0], "server": "https://monorail-edge.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL3dlYnBrZ2NhY2hlLmNvbQA=", false, 0], "server": "https://24timezones-com.webpkgcache.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://id.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovLzI0dGltZXpvbmVzLmNvbQA=", false, 0], "server": "https://24timezones-com.webpkgcache.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ogs.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395502931558806", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 41167}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395502931556414", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 39323}, "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395502927037367", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 46118}, "server": "https://www.googleadservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395502924166980", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 177895}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395502931890437", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 43840}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["MAAAACwAAABodHRwczovL2Nocm9tZS1kZXZ0b29scy1mcm9udGVuZC5hcHBzcG90LmNvbQ==", false, 0], "server": "https://chrome-devtools-frontend.appspot.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://hatchgolf.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2dzdGF0aWMuY29tAA==", false, 0], "network_stats": {"srtt": 37403}, "server": "https://encrypted-tbn0.gstatic.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", false, 0], "server": "https://a.nel.cloudflare.com", "supports_spdy": true}, {"anonymization": ["NAAAAC0AAABodHRwczovL2FjY291bnRjYXBhYmlsaXRpZXMtcGEuZ29vZ2xlYXBpcy5jb20AAAA=", false, 0], "server": "https://accountcapabilities-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://www.hatchgolf.com", "supports_spdy": true}, {"anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL25hdnlhaW1zLmNvbQ==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL25hdnlhaW1zLmNvbQ==", false, 0], "network_stats": {"srtt": 72303}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["IAAAABoAAABodHRwczovL3d3dy5nb29nbGVhcGlzLmNvbQAA", false, 0], "server": "https://www.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://a.nel.cloudflare.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://polyfill-fastly.net", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://swift.perfectapps.io", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", false, 0], "server": "https://checkout.pci.shopifyinc.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", false, 0], "server": "https://error-analytics-sessions-production.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", false, 0], "server": "https://cdn.shopify.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", false, 0], "server": "https://shop.app", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", false, 0], "server": "https://otlp-http-production.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://shop.app", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3Nob3AuYXBw", false, 0], "server": "https://monorail-edge.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://checkout.shopify.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://error-analytics-sessions-production.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://cdn.shopify.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395875452921043", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://maps.gstatic.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://cdn.shopify.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "server": "https://maps.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://otlp-http-production.shopifysvc.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://www.hatchgolf.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", false, 0], "server": "https://monorail-edge.shopifysvc.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397689848883613", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2hhdGNoZ29sZi5jb20AAAA=", true, 0], "network_stats": {"srtt": 28225}, "server": "https://fonts.gstatic.com"}], "supports_quic": {"address": "**************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G", "CAISABiAgICA+P////8B": "3G"}}}