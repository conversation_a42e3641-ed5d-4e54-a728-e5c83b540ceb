
DevTools remote debugging requires a non-default data directory. Specify this using --user-data-dir.

DevTools remote debugging requires a non-default data directory. Specify this using --user-data-dir.

DevTools remote debugging requires a non-default data directory. Specify this using --user-data-dir.

DevTools remote debugging requires a non-default data directory. Specify this using --user-data-dir.

DevTools remote debugging requires a non-default data directory. Specify this using --user-data-dir.

DevTools remote debugging requires a non-default data directory. Specify this using --user-data-dir.

DevTools listening on ws://127.0.0.1:50789/devtools/browser/6eb16fba-8d1d-43a0-8555-37b2d15a8696
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)
[1772:10272:0523/143713.236:ERROR:components\os_crypt\sync\os_crypt_win.cc:105] Failed to decrypt: The data is invalid. (0xD)

DevTools listening on ws://127.0.0.1:51132/devtools/browser/3654cac4-99be-4510-9cd8-51d3b8857a89

DevTools listening on ws://127.0.0.1:51181/devtools/browser/efc5a277-8286-4078-8b23-0ea7c78ae69b

DevTools listening on ws://127.0.0.1:51541/devtools/browser/0126578a-b436-4bdd-b402-13fc2eea88f7

DevTools listening on ws://127.0.0.1:51644/devtools/browser/8075f189-33d2-4c63-b4a9-f002a952c206

DevTools listening on ws://127.0.0.1:51677/devtools/browser/a32c4fea-91e4-4994-b657-7420d8e76474

DevTools listening on ws://127.0.0.1:51891/devtools/browser/ea9a06c6-7036-408e-89c6-cb3fd9686a45

DevTools listening on ws://127.0.0.1:52010/devtools/browser/2fc1edb2-7763-49eb-851b-00b0b4fe94f1

DevTools listening on ws://127.0.0.1:52179/devtools/browser/f9004d45-3a3b-4908-a818-ccdfc0369625
[35140:36340:0523/150125.855:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150125.963:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150126.076:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150126.185:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150126.626:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150126.855:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150127.165:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150128.093:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150128.236:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150130.006:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150132.916:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150134.236:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150135.318:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150135.436:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150135.715:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s
[35140:36340:0523/150137.332:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.136 s for parse duration 0.132 s

DevTools listening on ws://127.0.0.1:52340/devtools/browser/41dda349-30e1-4690-b500-281237d6d7da
[37496:31760:0523/150339.611:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150339.712:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150339.818:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150339.937:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150340.397:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150340.777:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150343.337:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150345.887:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150348.507:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150351.366:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150354.248:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150357.057:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150359.907:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150402.707:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150405.648:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150408.737:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150411.747:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150414.897:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150417.827:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s
[37496:31760:0523/150420.648:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.145 s for parse duration 0.139 s

DevTools listening on ws://127.0.0.1:52490/devtools/browser/0a98de99-bb87-445b-81d0-2cb44e1adfd0
[7984:26184:0523/150622.489:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT

DevTools listening on ws://127.0.0.1:52743/devtools/browser/0f83822c-15da-4b90-8919-8b9398ef5a09
[33544:19252:0523/150642.920:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:19252:0523/150643.032:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:18972:0523/150643.186:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[33544:19252:0523/150648.314:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:19252:0523/150648.321:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:19252:0523/150648.422:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:19252:0523/150649.392:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:19252:0523/150649.502:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:19252:0523/150649.612:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:19252:0523/150649.802:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s
[33544:19252:0523/150650.712:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.397 s for parse duration 0.386 s

DevTools listening on ws://127.0.0.1:55304/devtools/browser/b764f847-5564-47cf-9886-300a35743939

DevTools listening on ws://127.0.0.1:55414/devtools/browser/f71cef6e-46a4-4e68-8851-f0c9a42fb73a

DevTools listening on ws://127.0.0.1:55474/devtools/browser/1a348f7e-5136-4765-ba16-e21e1d106fb2

DevTools listening on ws://127.0.0.1:60220/devtools/browser/9e5386e3-c195-4aef-9b77-5e9a0f36fafe
[11964:18336:0523/215611.971:ERROR:components\device_event_log\device_event_log_impl.cc:202] [21:56:11.972] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)

DevTools listening on ws://127.0.0.1:61603/devtools/browser/67e1bfbc-238a-4164-b156-d4eec29de961

DevTools listening on ws://127.0.0.1:62911/devtools/browser/1e5770b9-2a6a-4baf-a0c2-8d6aca549dd8

DevTools listening on ws://127.0.0.1:63214/devtools/browser/c9b40b33-a806-4ed7-a8ac-caa57b05abba

DevTools listening on ws://127.0.0.1:63363/devtools/browser/c460d84e-bb9d-4794-81cd-997e0e0fc618

DevTools listening on ws://127.0.0.1:63473/devtools/browser/ceb13d59-1d72-487a-b405-098278fd1ea3

DevTools listening on ws://127.0.0.1:63628/devtools/browser/4cfb87f1-bd39-4cab-be0a-c5bbf1573bc9

DevTools listening on ws://127.0.0.1:63914/devtools/browser/811f056e-6bf8-46eb-ac52-6d5b0cf2380f

DevTools listening on ws://127.0.0.1:64247/devtools/browser/5b57715f-2fc1-440c-a170-22b697ae555d

DevTools listening on ws://127.0.0.1:64287/devtools/browser/f2885032-4973-4532-b713-f491cdcff528
[36904:12784:0524/012432.876:ERROR:components\device_event_log\device_event_log_impl.cc:202] [01:24:32.786] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)

DevTools listening on ws://127.0.0.1:60518/devtools/browser/99e564e6-298d-4147-95cf-ba456a631ec6

DevTools listening on ws://127.0.0.1:60904/devtools/browser/03ddb50e-5dea-4eec-b1c2-973caec2a430

DevTools listening on ws://127.0.0.1:61579/devtools/browser/2c7fc7db-dfb0-4506-8285-ad4fe94a2a3d

DevTools listening on ws://127.0.0.1:61685/devtools/browser/ed66063c-8743-4bb8-bcb4-ccbf14ff787c
[28336:31004:0528/195748.263:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195748.376:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195748.947:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195749.808:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195752.208:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195754.017:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195755.483:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195755.595:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195755.717:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195755.957:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s
[28336:31004:0528/195758.030:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.275 s for parse duration 0.262 s

DevTools listening on ws://127.0.0.1:61924/devtools/browser/ecb5ce12-71ae-4472-bd60-5875b82e4599
[31080:38312:0528/200047.634:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200047.748:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200047.779:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200047.880:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200048.490:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200048.837:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200049.160:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200051.019:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200052.498:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200054.078:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200055.739:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200057.508:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200059.468:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200101.448:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200103.149:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200104.838:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200106.149:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200107.298:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200108.739:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200110.078:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200111.359:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200112.668:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200113.828:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200115.178:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200116.520:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200117.828:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200119.210:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200120.451:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200121.800:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200123.279:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200124.541:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200126.088:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200127.609:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200129.059:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200129.228:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200130.308:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200130.439:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200131.268:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200131.378:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200132.019:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200133.449:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s
[31080:38312:0528/200133.922:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.201 s for parse duration 0.2 s

DevTools listening on ws://127.0.0.1:49270/devtools/browser/3516f567-dd82-44ab-a6b6-75af1d97884c

DevTools listening on ws://127.0.0.1:52021/devtools/browser/d9b0a321-9bc5-47ff-ba76-f56e4087d1c5

DevTools listening on ws://127.0.0.1:52158/devtools/browser/f3b77131-a350-45cb-962c-78f5e7373b6f

DevTools listening on ws://127.0.0.1:52315/devtools/browser/c8353a03-0b72-43ea-aded-86af33f04c01

DevTools listening on ws://127.0.0.1:52598/devtools/browser/9d1f40ad-83e9-4fc1-ab67-a81f86480500

DevTools listening on ws://127.0.0.1:52675/devtools/browser/90f185da-61f3-4e98-a7fa-5c4043a05adf

DevTools listening on ws://127.0.0.1:52732/devtools/browser/c6154d5a-ac7f-4455-8407-14c61f16f142

DevTools listening on ws://127.0.0.1:52841/devtools/browser/d47c6a5f-b5b6-4042-a3cf-86c0c766a390

DevTools listening on ws://127.0.0.1:53019/devtools/browser/cd5c6c6a-da4d-4fa6-bb0b-7467227909d8

DevTools listening on ws://127.0.0.1:53551/devtools/browser/b48b0752-8a15-4f0e-815b-7c72ad20ecf2

DevTools listening on ws://127.0.0.1:53644/devtools/browser/cc3787a6-0ff0-467f-ba99-0f976e95a936

DevTools listening on ws://127.0.0.1:54177/devtools/browser/091134ef-2592-4c1b-bb03-5fc76083ca3d

DevTools listening on ws://127.0.0.1:54275/devtools/browser/cad2f3c8-7ef2-4717-bdc0-fbd534aefbbc

DevTools listening on ws://127.0.0.1:54408/devtools/browser/571a0f54-1fb1-4b97-a5f9-4e658979a2f4

DevTools listening on ws://127.0.0.1:54580/devtools/browser/0e07a2c5-7e70-46e2-a69d-fcb256b06640

DevTools listening on ws://127.0.0.1:54740/devtools/browser/fc8a992b-ffba-4e01-9be4-16a6ac07139f

DevTools listening on ws://127.0.0.1:54867/devtools/browser/588313c9-a7ae-456d-a2a1-067e7b712b6e

DevTools listening on ws://127.0.0.1:55848/devtools/browser/e5f1afe9-3915-472b-9cc7-bb9eec1a59ce
[28164:31712:0601/222902.681:ERROR:components\device_event_log\device_event_log_impl.cc:202] [22:29:02.681] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)

DevTools listening on ws://127.0.0.1:56438/devtools/browser/918e9b4a-9702-452e-9ec0-fe719e50bb9e
[22428:14692:0601/223745.855:ERROR:components\device_event_log\device_event_log_impl.cc:202] [22:37:45.855] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)
[22428:14692:0602/033336.193:ERROR:components\device_event_log\device_event_log_impl.cc:202] [03:33:36.192] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)

DevTools listening on ws://127.0.0.1:65275/devtools/browser/0bb07170-f735-4371-ab38-e3a1f1653dfd

DevTools listening on ws://127.0.0.1:65413/devtools/browser/cf116d7d-469b-4802-967d-ecbbbde1a821

DevTools listening on ws://127.0.0.1:49162/devtools/browser/791b0e31-d7a4-49cf-ba36-bcb0a66ae398

DevTools listening on ws://127.0.0.1:49300/devtools/browser/c1bfd5bc-373b-4904-bb67-d246f1d785b9

DevTools listening on ws://127.0.0.1:49829/devtools/browser/c1583040-1147-434c-9832-3d6a9ecf10d9

DevTools listening on ws://127.0.0.1:51154/devtools/browser/ce845fc4-8ed8-4385-bbbb-a7c8ae5aaf80

DevTools listening on ws://127.0.0.1:51318/devtools/browser/4dc6961e-a203-4ecf-b368-e68a03474eb2

DevTools listening on ws://127.0.0.1:52414/devtools/browser/3000811f-f3fa-450f-a240-d9d4aee60aa3

DevTools listening on ws://127.0.0.1:52632/devtools/browser/176d3570-3500-4abc-8f9b-671f68a37e3f

DevTools listening on ws://127.0.0.1:52736/devtools/browser/4ef01c22-85d4-4abb-beaa-beca99e2ffd7

DevTools listening on ws://127.0.0.1:52856/devtools/browser/b32eeaa8-93f5-4609-adae-84fb7fd9b682

DevTools listening on ws://127.0.0.1:52985/devtools/browser/9bbbf321-f366-4544-872d-f6ceb8e4d8cf

DevTools listening on ws://127.0.0.1:53146/devtools/browser/07d10d08-f87b-49d9-924f-4881cc754a58

DevTools listening on ws://127.0.0.1:53216/devtools/browser/48562b8b-1f26-473e-906f-689d90ad06fc

DevTools listening on ws://127.0.0.1:53308/devtools/browser/cd1fc15f-73e3-4136-88b1-6ea632897f13

DevTools listening on ws://127.0.0.1:53485/devtools/browser/0ade7644-d46d-4d18-8374-e2b3d7e4ceeb

DevTools listening on ws://127.0.0.1:56508/devtools/browser/373c828c-0b71-4aa4-8bbd-d565433ada06

DevTools listening on ws://127.0.0.1:56672/devtools/browser/7cceb567-f53e-4fec-bc04-188a93115053

DevTools listening on ws://127.0.0.1:57032/devtools/browser/2d6c84f3-d258-40dd-988a-f499ba539b19

DevTools listening on ws://127.0.0.1:57135/devtools/browser/d3e0e3da-d420-41a2-9e3e-64fac898889b

DevTools listening on ws://127.0.0.1:57398/devtools/browser/c4a183d5-a36f-4972-aaee-63321f644292

DevTools listening on ws://127.0.0.1:57814/devtools/browser/90eb1e1d-6722-4bc4-a6ad-de946837abdd

DevTools listening on ws://127.0.0.1:58214/devtools/browser/e714f6c6-220f-4718-9949-f40a54c48c26
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:58329/devtools/browser/aa76b03e-0758-40a0-bb38-9bdd7668ee60
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:58419/devtools/browser/2c9c480e-3b7a-42f5-b85b-38171544d9e5
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:59637/devtools/browser/0c1846ac-1d33-4cfa-9896-52d3b41b4998
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:60049/devtools/browser/005b0def-5c35-4231-aa14-ab43c174ed3e
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:60404/devtools/browser/52bc9d36-b04e-42de-aa8e-91b87f6df678
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:51267/devtools/browser/caf5e1c6-b784-4b87-a468-ca8538d3d9b4
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:**********.196442   34744 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:58222/devtools/browser/9d9e33d7-1f0f-42ea-878d-92aa305f76f1
[27928:26932:0611/195513.541:ERROR:components\signin\internal\identity_manager\account_info_fetcher.cc:62] OnGetTokenFailure: Invalid credentials (credentials rejected by server).
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:**********.804038   22084 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
[27928:26932:0611/195532.342:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.168 s for parse duration 0.165 s
[27928:26932:0611/195532.447:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.168 s for parse duration 0.165 s
[27928:26932:0611/195532.481:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.168 s for parse duration 0.165 s
[27928:26932:0611/195532.547:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.168 s for parse duration 0.165 s
[27928:26932:0611/195532.658:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.168 s for parse duration 0.165 s
[27928:26932:0611/195533.437:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.168 s for parse duration 0.165 s
[27928:26932:0611/195533.667:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.168 s for parse duration 0.165 s
[27928:26932:0611/195541.920:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.168 s for parse duration 0.165 s
[27928:26932:0611/200002.883:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200002.990:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200003.091:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200003.714:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200003.873:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200004.332:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200004.472:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200005.103:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200005.241:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200006.022:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.155 s for parse duration 0.154 s
[27928:26932:0611/200006.161:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200006.249:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200006.357:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200006.827:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200007.286:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200009.177:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200009.318:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200010.146:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200010.287:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s
[27928:26932:0611/200010.730:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.136 s

DevTools listening on ws://127.0.0.1:49734/devtools/browser/d655d43f-348b-4431-aec5-91bb0d2ee5f9
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1750251372.733298   29276 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
[37212:1280:0618/195852.074:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195852.191:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195852.206:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195852.308:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195852.428:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195852.908:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195853.019:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195853.420:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195854.869:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195856.498:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195857.829:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195859.372:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195900.822:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195902.587:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195904.159:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195905.511:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195906.992:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195908.471:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195909.816:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195911.348:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195912.700:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195913.987:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195915.338:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195916.758:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195918.008:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195919.259:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195920.857:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195922.438:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195923.758:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195925.125:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195926.756:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195928.089:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195929.418:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195930.827:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195932.417:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195933.879:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195935.110:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195936.621:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195938.443:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195940.316:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195942.018:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195943.532:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195945.015:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195946.364:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195947.905:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195949.371:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195951.008:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195952.129:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195952.508:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195953.867:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195955.247:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195956.718:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195957.988:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/195959.398:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/200000.073:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/200000.181:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/200000.818:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/200001.818:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.244 s for parse duration 0.24 s
[37212:1280:0618/200002.042:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200002.149:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200002.363:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200002.818:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200003.038:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200003.569:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200004.660:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200005.181:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200005.279:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200006.101:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.227 s for parse duration 0.217 s
[37212:1280:0618/200006.260:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200006.358:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200006.460:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200006.962:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200007.242:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200009.341:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200010.212:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200010.351:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200011.332:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200011.629:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.161 s for parse duration 0.152 s
[37212:1280:0618/200011.777:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200011.874:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200011.982:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200012.413:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200012.792:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200014.023:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200014.155:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200014.272:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200018.530:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.153 s for parse duration 0.145 s
[37212:1280:0618/200018.695:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200018.807:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200018.911:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200019.318:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200019.689:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200026.826:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200026.999:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200027.162:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200028.009:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200029.579:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200030.275:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200043.546:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/200059.914:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/202414.566:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/202414.689:ERROR:components\page_load_metrics\browser\page_load_metrics_update_dispatcher.cc:120] Invalid parse_blocked_on_script_execution_duration 0.166 s for parse duration 0.163 s
[37212:1280:0618/202415.356:ERROR:content\browser\network_service_instance_impl.cc:606] Network service crashed, restarting service.
[37212:1280:0618/202415.613:ERROR:content\browser\gpu\gpu_process_host.cc:955] GPU process exited unexpectedly: exit_code=1

DevTools listening on ws://127.0.0.1:58813/devtools/browser/1627da5c-8ce1-4c22-b431-a53b3595f512
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1750524464.136872   20116 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).
[26688:35708:0622/000129.871:ERROR:components\device_event_log\device_event_log_impl.cc:202] [00:01:29.870] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)
[26688:35708:0622/000132.015:ERROR:content\browser\network_service_instance_impl.cc:606] Network service crashed, restarting service.
[26688:35708:0622/000132.208:ERROR:content\browser\gpu\gpu_process_host.cc:955] GPU process exited unexpectedly: exit_code=1

DevTools listening on ws://127.0.0.1:59883/devtools/browser/b07d3a35-ce92-4bd9-b712-38953f91b97e
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1750525343.742281   35300 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).
[34704:30252:0622/032804.045:ERROR:components\device_event_log\device_event_log_impl.cc:202] [03:28:04.039] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)

DevTools listening on ws://127.0.0.1:50671/devtools/browser/47dba1fa-5f25-4f3a-b470-eb4fc7f4b302
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1750537703.931988   29084 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:51062/devtools/browser/7012513c-b882-4e52-8981-93c149c5a9b4
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1750537870.210739   42684 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).

DevTools listening on ws://127.0.0.1:64609/devtools/browser/165c5c42-87b7-4cb2-aa59-977a939ee80f
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1750611841.038313   33244 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).
[33608:30240:0623/000412.212:ERROR:components\device_event_log\device_event_log_impl.cc:202] [00:04:12.212] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)

DevTools listening on ws://127.0.0.1:63635/devtools/browser/14c726a2-9205-4e09-b50d-00a4a4a8dfce
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1750611913.289848   22644 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).
[34308:29300:0623/033006.060:ERROR:components\device_event_log\device_event_log_impl.cc:202] [03:30:06.060] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)
