var WcpConsent;!function(){var e={229:function(e){window,e.exports=function(e){var t={};function o(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,o),r.l=!0,r.exports}return o.m=e,o.c=t,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(n,r,function(t){return e[t]}.bind(null,r));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=3)}([function(e,t,o){(t=o(2)(!1)).push([e.i,'.w8hcgFksdo30C8w-bygqu{color:#000}.ydkKdaztSS0AeHWIeIHsQ a{color:#0067B8}.erL690_8JwUW-R4bJRcfl{background-color:#EBEBEB;border:none;color:#000}.erL690_8JwUW-R4bJRcfl:enabled:hover{color:#000;background-color:#DBDBDB;box-shadow:0px 4px 10px rgba(0,0,0,0.25);border:none}.erL690_8JwUW-R4bJRcfl:enabled:focus{background-color:#DBDBDB;box-shadow:0px 4px 10px rgba(0,0,0,0.25);border:2px solid #000}.erL690_8JwUW-R4bJRcfl:disabled{opacity:1;color:rgba(0,0,0,0.2);background-color:rgba(0,0,0,0.2);border:none}._1zNQOqxpBFSokeCLGi_hGr{border:none;background-color:#0067B8;color:#fff}._1zNQOqxpBFSokeCLGi_hGr:enabled:hover{color:#fff;background-color:#0067B8;box-shadow:0px 4px 10px rgba(0,0,0,0.25);border:none}._1zNQOqxpBFSokeCLGi_hGr:enabled:focus{background-color:#0067B8;box-shadow:0px 4px 10px rgba(0,0,0,0.25);border:2px solid #000}._1zNQOqxpBFSokeCLGi_hGr:disabled{opacity:1;color:rgba(0,0,0,0.2);background-color:rgba(0,120,215,0.2);border:none}._23tra1HsiiP6cT-Cka-ycB{position:relative;display:flex;z-index:9999;width:100%;background-color:#F2F2F2;justify-content:space-between;text-align:left}div[dir="rtl"]._23tra1HsiiP6cT-Cka-ycB{text-align:right}._1Upc2NjY8AlDn177YoVj0y{margin:0;padding-left:5%;padding-top:8px;padding-bottom:8px}div[dir="rtl"] ._1Upc2NjY8AlDn177YoVj0y{margin:0;padding:8px 5% 8px 0;float:none}._23tra1HsiiP6cT-Cka-ycB svg{fill:none;max-width:none;max-height:none}._1V_hlU-7jdtPiooHMu89BB{display:table-cell;padding:12px;width:24px;height:24px;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:24px;line-height:0}.f6QKJD7fhSbnJLarTL-W-{display:table-cell;vertical-align:middle;padding:0;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:13px;line-height:16px}.f6QKJD7fhSbnJLarTL-W- a{text-decoration:underline}._2j0fmugLb1FgYz6KPuB91w{display:inline-block;margin-left:5%;margin-right:5%;min-width:40%;min-width:calc((150px + 3 * 4px) * 2 + 150px);min-width:-webkit-fit-content;min-width:-moz-fit-content;min-width:fit-content;align-self:center;position:relative}._1XuCi2WhiqeWRUVp3pnFG3{margin:4px;padding:5px;min-width:150px;min-height:36px;vertical-align:top;cursor:pointer;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px;text-align:center}._1XuCi2WhiqeWRUVp3pnFG3:focus{box-sizing:border-box}._1XuCi2WhiqeWRUVp3pnFG3:disabled{cursor:not-allowed}._2bvsb3ubApyZ0UGoQA9O9T{display:block;position:fixed;z-index:10000;top:0;left:0;width:100%;height:100%;background-color:rgba(255,255,255,0.6);overflow:auto;text-align:left}div[dir="rtl"]._2bvsb3ubApyZ0UGoQA9O9T{text-align:right}div[dir="rtl"] ._2bvsb3ubApyZ0UGoQA9O9T{left:auto;right:0}.AFsJE948muYyzCMktdzuk{position:relative;top:8%;margin-bottom:40px;margin-left:auto;margin-right:auto;box-sizing:border-box;width:640px;background-color:#fff;border:1px solid #0067B8}._3kWyBRbW_dgnMiEyx06Fu4{float:right;z-index:1;margin:2px;padding:12px;border:none;cursor:pointer;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:13px;line-height:13px;display:flex;align-items:center;text-align:center;color:#666;background-color:#fff}div[dir="rtl"] ._3kWyBRbW_dgnMiEyx06Fu4{margin:2px;padding:12px;float:left}.uCYvKvHXrhjNgflv1VqdD{position:static;margin-top:36px;margin-left:36px;margin-right:36px}._17pX1m9O_W--iZbDt3Ta5r{margin-top:0;margin-bottom:12px;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:600;font-size:20px;line-height:24px;text-transform:none}._1kBkHQ1V1wu3kl-YcLgUr6{height:446px;overflow:auto}._20_nXDf6uFs9Q6wxRXG-I-{margin-top:0;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px}._20_nXDf6uFs9Q6wxRXG-I- a{text-decoration:underline}dl._2a0NH_GDQEQe5Ynfo7suVH{margin-top:36px;margin-bottom:0;padding:0;list-style:none;text-transform:none}dt._3j_LCPv7fyXv3A8FIXVwZ4{margin-top:20px;float:none;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:600;font-size:18px;line-height:24px;list-style:none}.k-vxTGFbdq1aOZB2HHpjh{margin:0;padding:0;border:none}._2Bucyy75c_ogoU1g-liB5R{margin:0;padding:0;border-bottom:none;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:600;font-size:18px;line-height:24px;text-transform:none}._63gwfzV8dclrsl2cfd90r{display:inline-block;margin-top:0;margin-bottom:13px;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px}._1l8wM_4mRYGz3Iu7l3BZR7{display:block}._2UE03QS02aZGkslegN_F-i{display:inline-block;position:relative;left:5px;margin-bottom:13px;margin-right:34px;padding:3px}div[dir="rtl"] ._2UE03QS02aZGkslegN_F-i{margin:0 0 13px 34px;padding:3px;float:none}div[dir="rtl"] ._2UE03QS02aZGkslegN_F-i{left:auto;right:5px}._23tra1HsiiP6cT-Cka-ycB *::before,._2bvsb3ubApyZ0UGoQA9O9T *::before,._23tra1HsiiP6cT-Cka-ycB *::after,._2bvsb3ubApyZ0UGoQA9O9T *::after{box-sizing:inherit}._1HSFn0HzGo6w4ADApV8-c4{outline:2px solid rgba(0,0,0,0.8)}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2{display:inline-block;position:relative;margin-top:0;margin-left:0;margin-right:0;height:0;width:0;border-radius:0;cursor:pointer;outline:none;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2+label::before{display:block;position:absolute;top:5px;left:3px;height:19px;width:19px;content:"";border-radius:50%;border:1px solid #000;background-color:#fff}div[dir="rtl"] input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2+label::before{left:auto;right:3px}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:hover::before{border:1px solid #0067B8}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:hover::after{display:block;position:absolute;top:10px;left:8px;height:9px;width:9px;content:"";border-radius:50%;background-color:rgba(0,0,0,0.8)}div[dir="rtl"] input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:hover::after{left:auto;right:8px}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:focus::before{border:1px solid #0067B8}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:focus::after{display:block;position:absolute;top:10px;left:8px;height:9px;width:9px;content:"";border-radius:50%;background-color:#000}div[dir="rtl"] input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:focus::after{left:auto;right:8px}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:checked+label::after{display:block;position:absolute;top:10px;left:8px;height:9px;width:9px;content:"";border-radius:50%;background-color:#000}div[dir="rtl"] input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:checked+label::after{left:auto;right:8px}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:disabled+label{cursor:not-allowed}input[type="radio"]._1dp8Vp5m3HwAqGx8qBmFV2:disabled+label::before{border:1px solid rgba(0,0,0,0.2);background-color:rgba(0,0,0,0.2)}._3RJzeL3l9Rl_lAQEm6VwdX{display:block;position:static;float:right;margin-top:0;margin-bottom:0;margin-left:19px;margin-right:0;padding-top:0;padding-bottom:0;padding-left:8px;padding-right:0;width:80%;width:calc(100% - 19px);font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px;text-transform:none;cursor:pointer;box-sizing:border-box}div[dir="rtl"] ._3RJzeL3l9Rl_lAQEm6VwdX{margin:0 19px 0 0;padding:0 8px 0 0;float:left}.nohp3sIG12ZBhzcMnPala{margin-top:20px;margin-bottom:48px}._2uhaEsmeotZ3P-M0AXo2kF{padding:0;width:278px;height:36px;cursor:pointer;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px;text-align:center}._2uhaEsmeotZ3P-M0AXo2kF:focus{box-sizing:border-box}._2uhaEsmeotZ3P-M0AXo2kF:disabled{cursor:not-allowed}._3tOu1FJ59c_xz_PmI1lKV5{float:right;padding:0;width:278px;height:36px;cursor:pointer;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px;text-align:center}._3tOu1FJ59c_xz_PmI1lKV5:focus{box-sizing:border-box}._3tOu1FJ59c_xz_PmI1lKV5:disabled{cursor:not-allowed}div[dir="rtl"] ._3tOu1FJ59c_xz_PmI1lKV5{margin:0;padding:0;float:left}@media only screen and (max-width: 768px){._2j0fmugLb1FgYz6KPuB91w,._1Upc2NjY8AlDn177YoVj0y{padding-top:8px;padding-bottom:12px;padding-left:3.75%;padding-right:3.75%;margin:0;width:92.5%}._23tra1HsiiP6cT-Cka-ycB{display:block}._1XuCi2WhiqeWRUVp3pnFG3{margin-bottom:8px;margin-left:0;margin-right:0;width:100%}._2bvsb3ubApyZ0UGoQA9O9T{overflow:hidden}.AFsJE948muYyzCMktdzuk{top:1.8%;width:93.33%;height:96.4%;overflow:hidden}.uCYvKvHXrhjNgflv1VqdD{margin-top:24px;margin-left:24px;margin-right:24px;height:100%}._1kBkHQ1V1wu3kl-YcLgUr6{height:62%;height:calc(100% - 188px);min-height:50%}._2uhaEsmeotZ3P-M0AXo2kF{width:100%}._3tOu1FJ59c_xz_PmI1lKV5{margin-bottom:12px;margin-left:0;width:100%}div[dir="rtl"] ._3tOu1FJ59c_xz_PmI1lKV5{margin:0 0 12px 0;padding:0;float:none}}@media only screen and (max-width: 768px) and (orientation: landscape), only screen and (max-height: 260px), only screen and (max-width: 340px){.AFsJE948muYyzCMktdzuk{overflow:auto}}@media only screen and (max-height: 260px), only screen and (max-width: 340px){._1XuCi2WhiqeWRUVp3pnFG3{min-width:0}._3kWyBRbW_dgnMiEyx06Fu4{padding:3%}.uCYvKvHXrhjNgflv1VqdD{margin-top:3%;margin-left:3%;margin-right:3%}._17pX1m9O_W--iZbDt3Ta5r{margin-bottom:3%}._1kBkHQ1V1wu3kl-YcLgUr6{height:calc(79% - 64px)}.nohp3sIG12ZBhzcMnPala{margin-top:5%;margin-bottom:10%}._3tOu1FJ59c_xz_PmI1lKV5{margin-bottom:3%}div[dir="rtl"] ._3tOu1FJ59c_xz_PmI1lKV5{margin:0 0 3% 0;padding:0;float:none}}\n',""]),t.locals={textColorTheme:"w8hcgFksdo30C8w-bygqu",hyperLinkTheme:"ydkKdaztSS0AeHWIeIHsQ",secondaryButtonTheme:"erL690_8JwUW-R4bJRcfl",primaryButtonTheme:"_1zNQOqxpBFSokeCLGi_hGr",bannerBody:"_23tra1HsiiP6cT-Cka-ycB",bannerInform:"_1Upc2NjY8AlDn177YoVj0y",infoIcon:"_1V_hlU-7jdtPiooHMu89BB",bannerInformBody:"f6QKJD7fhSbnJLarTL-W-",buttonGroup:"_2j0fmugLb1FgYz6KPuB91w",bannerButton:"_1XuCi2WhiqeWRUVp3pnFG3",cookieModal:"_2bvsb3ubApyZ0UGoQA9O9T",modalContainer:"AFsJE948muYyzCMktdzuk",closeModalIcon:"_3kWyBRbW_dgnMiEyx06Fu4",modalBody:"uCYvKvHXrhjNgflv1VqdD",modalTitle:"_17pX1m9O_W--iZbDt3Ta5r",modalContent:"_1kBkHQ1V1wu3kl-YcLgUr6",cookieStatement:"_20_nXDf6uFs9Q6wxRXG-I-",cookieOrderedList:"_2a0NH_GDQEQe5Ynfo7suVH",cookieListItem:"_3j_LCPv7fyXv3A8FIXVwZ4",cookieListItemGroup:"k-vxTGFbdq1aOZB2HHpjh",cookieListItemTitle:"_2Bucyy75c_ogoU1g-liB5R",cookieListItemDescription:"_63gwfzV8dclrsl2cfd90r",cookieItemRadioBtnGroup:"_1l8wM_4mRYGz3Iu7l3BZR7",cookieItemRadioBtnCtrl:"_2UE03QS02aZGkslegN_F-i",cookieItemRadioBtnCtrlOutline:"_1HSFn0HzGo6w4ADApV8-c4",cookieItemRadioBtn:"_1dp8Vp5m3HwAqGx8qBmFV2",cookieItemRadioBtnLabel:"_3RJzeL3l9Rl_lAQEm6VwdX",modalButtonGroup:"nohp3sIG12ZBhzcMnPala",modalButtonReset:"_2uhaEsmeotZ3P-M0AXo2kF",modalButtonSave:"_3tOu1FJ59c_xz_PmI1lKV5"},e.exports=t},function(e,t,o){"use strict";var n,r=function(){var e={};return function(t){if(void 0===e[t]){var o=document.querySelector(t);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}e[t]=o}return e[t]}}(),i=[];function a(e){for(var t=-1,o=0;o<i.length;o++)if(i[o].identifier===e){t=o;break}return t}function l(e,t){for(var o={},n=[],r=0;r<e.length;r++){var l=e[r],c=t.base?l[0]+t.base:l[0],s=o[c]||0,d="".concat(c," ").concat(s);o[c]=s+1;var u=a(d),p={css:l[1],media:l[2],sourceMap:l[3]};-1!==u?(i[u].references++,i[u].updater(p)):i.push({identifier:d,updater:h(p,t),references:1}),n.push(d)}return n}function c(e){var t=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var i=o.nc;i&&(n.nonce=i)}if(Object.keys(n).forEach((function(e){t.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(t);else{var a=r(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var s,d=(s=[],function(e,t){return s[e]=t,s.filter(Boolean).join("\n")});function u(e,t,o,n){var r=o?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=d(t,r);else{var i=document.createTextNode(r),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(i,a[t]):e.appendChild(i)}}function p(e,t,o){var n=o.css,r=o.media,i=o.sourceMap;if(r?e.setAttribute("media",r):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var b=null,m=0;function h(e,t){var o,n,r;if(t.singleton){var i=m++;o=b||(b=c(t)),n=u.bind(null,o,i,!1),r=u.bind(null,o,i,!0)}else o=c(t),n=p.bind(null,o,t),r=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(o)};return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else r()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=(void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n));var o=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<o.length;n++){var r=a(o[n]);i[r].references--}for(var c=l(e,t),s=0;s<o.length;s++){var d=a(o[s]);0===i[d].references&&(i[d].updater(),i.splice(d,1))}o=c}}}},function(e,t,o){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o=function(e,t){var o,n,r,i=e[1]||"",a=e[3];if(!a)return i;if(t&&"function"==typeof btoa){var l=(o=a,n=btoa(unescape(encodeURIComponent(JSON.stringify(o)))),r="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(n),"/*# ".concat(r," */")),c=a.sources.map((function(e){return"/*# sourceURL=".concat(a.sourceRoot||"").concat(e," */")}));return[i].concat(c).concat([l]).join("\n")}return[i].join("\n")}(t,e);return t[2]?"@media ".concat(t[2]," {").concat(o,"}"):o})).join("")},t.i=function(e,o,n){"string"==typeof e&&(e=[[null,e,""]]);var r={};if(n)for(var i=0;i<this.length;i++){var a=this[i][0];null!=a&&(r[a]=!0)}for(var l=0;l<e.length;l++){var c=[].concat(e[l]);n&&r[c[0]]||(o&&(c[2]?c[2]="".concat(o," and ").concat(c[2]):c[2]=o),t.push(c))}},t}},function(e,t,o){"use strict";o.r(t),o.d(t,"ConsentControl",(function(){return f}));var n=o(0),r=o(1),i=function(){function e(){}return e.escapeHtml=function(e){return e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"):""},e}(),a=n.locals,l=function(){function e(e,t,o,n,r,i,a){this.direction="ltr",this.previousFocusElementBeforePopup=null,this.cookieCategories=e,this.textResources=t,this.cookieCategoriesPreferences=o,this.containerElement=n,this.direction=r,this.isDirty=i,this.onPreferencesClosed=a}return e.prototype.createPreferencesDialog=function(){var e='\n        <div role="presentation" tabindex="-1"></div>\n        <div role="dialog" aria-modal="true" aria-label="'+i.escapeHtml(this.textResources.preferencesDialogTitle)+'" class="'+a.modalContainer+'" tabindex="-1">\n            <button aria-label="'+i.escapeHtml(this.textResources.preferencesDialogCloseLabel)+'" class="'+a.closeModalIcon+'" tabindex="0">&#x2715;</button>\n            <div role="document" class="'+a.modalBody+'">\n                <div>\n                    <h1 class="'+a.modalTitle+" "+a.textColorTheme+'">'+i.escapeHtml(this.textResources.preferencesDialogTitle)+'</h1>\n                </div>\n                \n                <form class="'+a.modalContent+" "+a.hyperLinkTheme+'">\n                    <p class="'+a.cookieStatement+" "+a.textColorTheme+'">\n                        '+this.textResources.preferencesDialogDescHtml+'\n                    </p>\n\n                    <dl class="'+a.cookieOrderedList+'">\n                    </dl>\n                </form>\n                \n                <div class="'+a.modalButtonGroup+'">\n                    <button type="button" aria-label="'+i.escapeHtml(this.textResources.saveLabel)+'" class="'+a.modalButtonSave+" "+a.primaryButtonTheme+'" disabled>'+i.escapeHtml(this.textResources.saveLabel)+'</button>\n                    <button type="button" aria-label="'+i.escapeHtml(this.textResources.resetLabel)+'" class="'+a.modalButtonReset+" "+a.secondaryButtonTheme+'" disabled>'+i.escapeHtml(this.textResources.resetLabel)+"</button>\n                </div>\n            </div>\n        </div>\n        ",t=document.createElement("div");t.setAttribute("id","wcpCookiePreferenceCtrl"),t.setAttribute("class",a.cookieModal),t.setAttribute("dir",this.direction),t.innerHTML=e,this.containerElement.appendChild(t);for(var o=!1,n=0,r=this.cookieCategories;n<r.length;n++){var l=r[n];if(l.isUnswitchable){var c='\n                <dt class="'+a.cookieListItem+" "+a.textColorTheme+'" aria-label="'+i.escapeHtml(l.name)+'">\n                    <h2 class="'+a.cookieListItemTitle+" "+a.textColorTheme+'">'+i.escapeHtml(l.name)+'</h2>\n                    <p class="'+a.cookieListItemDescription+" "+a.textColorTheme+'">'+l.descHtml+"</p>\n                </dt>\n                ";document.getElementsByClassName(a.cookieOrderedList)[0].innerHTML+=c}else{void 0!==this.cookieCategoriesPreferences[l.id]&&(o=!0);var s=l.id,d=!0===this.cookieCategoriesPreferences[l.id]?"checked":"",u=!1===this.cookieCategoriesPreferences[l.id]?"checked":"",p=a.cookieItemRadioBtn+"_"+s+"_accept",b=a.cookieItemRadioBtn+"_"+s+"_reject",m='<input type="radio" class="'+a.cookieItemRadioBtn+'" name="'+s+'" id="'+p+'" value="accept" '+d+">",h='<input type="radio" class="'+a.cookieItemRadioBtn+'" name="'+s+'" id="'+b+'" value="reject" '+u+">",f=a.cookieListItemTitle+"_"+s+"_title";c='\n                <dt class="'+a.cookieListItem+" "+a.textColorTheme+'" aria-label="'+i.escapeHtml(l.name)+'">\n                    <div class="'+a.cookieListItemGroup+'" role="radiogroup" aria-labelledby="'+f+'">\n                        <h2 class="'+a.cookieListItemTitle+" "+a.textColorTheme+'" id="'+f+'">'+i.escapeHtml(l.name)+'</h2>\n                        <p class="'+a.cookieListItemDescription+" "+a.textColorTheme+'">'+l.descHtml+'</p>\n                        <div class="'+a.cookieItemRadioBtnGroup+'">\n                            <div class="'+a.cookieItemRadioBtnCtrl+'">\n                                '+m+'\n                                <label class="'+a.cookieItemRadioBtnLabel+" "+a.textColorTheme+'" for="'+p+'">'+i.escapeHtml(this.textResources.acceptLabel)+'</label>\n                            </div>\n                            <div class="'+a.cookieItemRadioBtnCtrl+'">\n                                '+h+'\n                                <label class="'+a.cookieItemRadioBtnLabel+" "+a.textColorTheme+'" for="'+b+'">'+i.escapeHtml(this.textResources.rejectLabel)+"</label>\n                            </div>\n                        </div>\n                    </div>\n                </dt>\n                ",document.getElementsByClassName(a.cookieOrderedList)[0].innerHTML+=c}}if(o){var g=document.getElementsByClassName(a.modalButtonReset)[0];g&&(g.disabled=!1)}this.addPreferencesButtonsEvent()},e.prototype.onPreferencesDialogShowing=function(){var e=this,t=!1;this.previousFocusElementBeforePopup&&(t=!0),this.previousFocusElementBeforePopup=document.activeElement;var o=document.getElementsByClassName(a.modalContainer)[0];o.focus(),t||o.addEventListener("keydown",(function(t){"Escape"!=t.key&&"Esc"!=t.key||(t.preventDefault(),e.hidePreferencesDialog())}))},e.prototype.hidePreferencesDialog=function(){var e,t=document.getElementsByClassName(a.cookieModal)[0];this.containerElement.removeChild(t),null===(e=this.previousFocusElementBeforePopup)||void 0===e||e.focus(),this.previousFocusElementBeforePopup=null,this.onPreferencesClosed()},e.prototype.addPreferencesButtonsEvent=function(){var e=this,t=document.getElementsByClassName(a.closeModalIcon)[0],o=[].slice.call(document.getElementsByClassName(a.cookieItemRadioBtn)),n=document.getElementsByClassName(a.modalButtonSave)[0],r=document.getElementsByClassName(a.modalButtonReset)[0];this.controlRadioBtnFocusStyle(),this.controlNextActiveElement(),null==t||t.addEventListener("click",(function(){return e.hidePreferencesDialog()}));for(var i=function(t){t.addEventListener("click",(function(){var o=t.getAttribute("name");if(o){var r=e.cookieCategoriesPreferences[o],i=t.getAttribute("value");e.cookieCategoriesPreferences[o]="accept"===i,r!==e.cookieCategoriesPreferences[o]&&(n.disabled=!1,e.isDirty.changed=!0)}}))},l=0,c=o;l<c.length;l++)i(c[l]);null==r||r.addEventListener("click",(function(){n.disabled=!1;for(var t=0,o=e.cookieCategories;t<o.length;t++){var r=o[t];r.isUnswitchable||(e.cookieCategoriesPreferences[r.id]=void 0)}e.isDirty.changed=!1,e.setRadioBtnState()})),this.isDirty.changed&&(n.disabled=!1)},e.prototype.controlNextActiveElement=function(){var e=document.getElementsByClassName(a.closeModalIcon)[0],t=document.getElementsByClassName(a.modalButtonSave)[0],o=document.getElementsByClassName(a.modalButtonReset)[0],n=[].slice.call(document.getElementsByClassName(a.cookieItemRadioBtn)),r=null,i=null;n.length&&(r=n[n.length-2],i=n[n.length-1]);var l=function(t){"Tab"!=t.key||t.shiftKey||(t.preventDefault(),e.focus())},c=function(e){"Tab"==e.key&&e.shiftKey&&(e.preventDefault(),null==i||i.focus())},s=function(e){"Tab"==e.key&&e.shiftKey&&(e.preventDefault(),o.focus())};o.addEventListener("keydown",(function(t){"Tab"!=t.key||t.shiftKey||(t.preventDefault(),e.focus())})),o.disabled&&t.disabled?n.length&&(null==r||r.addEventListener("keydown",l),null==i||i.addEventListener("keydown",l),e.addEventListener("keydown",c)):e.addEventListener("keydown",s);for(var d=0,u=n;d<u.length;d++)u[d].addEventListener("click",(function(){o.disabled&&(o.disabled=!1,null==r||r.removeEventListener("keydown",l),null==i||i.removeEventListener("keydown",l),e.removeEventListener("keydown",c),e.addEventListener("keydown",s))}))},e.prototype.controlRadioBtnFocusStyle=function(){for(var e=this,t=0,o=[].slice.call(document.getElementsByClassName(a.cookieItemRadioBtn));t<o.length;t++){var n=o[t];n.addEventListener("blur",(function(t){var o=t.target;e.removeBlurRadioBtnOutline(o)})),n.addEventListener("focus",(function(e){e.target.parentElement.className+=" "+a.cookieItemRadioBtnCtrlOutline}))}},e.prototype.removeBlurRadioBtnOutline=function(e){if(e){var t=e.parentElement,o=t.className.replace(" "+a.cookieItemRadioBtnCtrlOutline,"");t.className=o}},e.prototype.addSaveButtonEvent=function(e){var t=document.getElementsByClassName(a.modalButtonSave)[0];null==t||t.addEventListener("click",(function(){return e()}))},e.prototype.setRadioBtnState=function(){for(var e=0,t=0,o=this.cookieCategories;t<o.length;t++){var n=o[t];if(!n.isUnswitchable){var r=n.id;!0===this.cookieCategoriesPreferences[r]?(document.getElementsByClassName(a.cookieItemRadioBtn)[e].checked=!0,e++,document.getElementsByClassName(a.cookieItemRadioBtn)[e].checked=!1,e++):!1===this.cookieCategoriesPreferences[r]?(document.getElementsByClassName(a.cookieItemRadioBtn)[e].checked=!1,e++,document.getElementsByClassName(a.cookieItemRadioBtn)[e].checked=!0,e++):(document.getElementsByClassName(a.cookieItemRadioBtn)[e].checked=!1,e++,document.getElementsByClassName(a.cookieItemRadioBtn)[e].checked=!1,e++)}}},e}(),c=n.locals,s=function(){function e(){}return e.createTheme=function(e,t){if(!t["background-color-between-page-and-dialog"]){var o=t["dialog-background-color"];this.setMissingColorFromAnotherProperty("background-color-between-page-and-dialog",o,.6,e)}if(t["primary-button-text-color"]||(e["primary-button-text-color"]=t["dialog-background-color"]),t["primary-button-disabled-text-color"]||(e["primary-button-disabled-text-color"]=t["dialog-background-color"]),t["dialog-border-color"]||(e["dialog-border-color"]=t["primary-button-color"]),t["hyperlink-font-color"]||(e["hyperlink-font-color"]=t["primary-button-color"]),t["primary-button-hover-color"]||(e["primary-button-hover-color"]=t["primary-button-color"]),t["primary-button-disabled-color"]||(e["primary-button-disabled-color"]=t["primary-button-color"]),t["primary-button-border"]||(e["primary-button-border"]="1px solid "+t["primary-button-color"]),t["primary-button-focus-border-color"]||(e["primary-button-focus-border-color"]=t["primary-button-color"]),t["radio-button-hover-border-color"]||(e["radio-button-hover-border-color"]=t["primary-button-color"]),t["secondary-button-text-color"]||(e["secondary-button-text-color"]=t["text-color"]),t["secondary-button-disabled-text-color"]||(e["secondary-button-disabled-text-color"]=t["text-color"]),t["radio-button-border-color"]||(e["radio-button-border-color"]=t["text-color"]),t["radio-button-checked-background-color"]||(e["radio-button-checked-background-color"]=t["text-color"]),t["radio-button-hover-background-color"]||(o=t["text-color"],this.setMissingColorFromAnotherProperty("radio-button-hover-background-color",o,.8,e)),t["radio-button-disabled-color"]||(o=t["text-color"],this.setMissingColorFromAnotherProperty("radio-button-disabled-color",o,.2,e)),t["radio-button-disabled-border-color"]||(o=t["text-color"],this.setMissingColorFromAnotherProperty("radio-button-disabled-border-color",o,.2,e)),t["secondary-button-hover-color"]||(e["secondary-button-hover-color"]=t["secondary-button-color"]),t["secondary-button-disabled-border"]||(e["secondary-button-disabled-border"]="1px solid "+t["secondary-button-disabled-color"]),t["secondary-button-hover-border"]||(e["secondary-button-hover-border"]=t["secondary-button-border"]),!t["secondary-button-focus-border-color"]){var n=t["secondary-button-border"].split(" ");e["secondary-button-focus-border-color"]=n[n.length-1]}},e.createThemeStyle=function(e){var t=document.createElement("style");t.type="text/css",t.id="ms-consent-banner-theme-styles",e&&t.setAttribute("nonce",e),document.head.appendChild(t)},e.applyTheme=function(e){var t="";t+="."+c.bannerBody+" {\n            background-color: "+e["banner-background-color"]+" !important;\n        }",t+="."+c.textColorTheme+" {\n            color: "+e["text-color"]+" !important;\n        }",t+="."+c.hyperLinkTheme+" a {\n            color: "+e["hyperlink-font-color"]+" !important;\n        }",t+=this.buildDialogStyle(e),t+=this.buildPrimaryBtnStyle(e),t+=this.buildSecondaryBtnStyle(e),t+=this.buildRadioBtnStyle(e),document.getElementById("ms-consent-banner-theme-styles").textContent=t},e.setMissingColorFromAnotherProperty=function(e,t,o,n){if(t.startsWith("#")){var r=parseInt(t.substring(1),16),i=r>>16&255,a=r>>8&255,l=255&r;n[e]="rgba("+i+", "+a+", "+l+", "+o+")"}else if(t.startsWith("rgb(")){var c="rgba"+t.substring(3,t.length-1)+", "+o+")";n[e]=c}else if(t.startsWith("rgba")){var s=t.lastIndexOf(","),d=t.substring(s+1,t.length-1),u=parseFloat(d.trim())*o;c=t.substring(0,s+1)+u.toFixed(2)+")",n[e]=c}},e.buildDialogStyle=function(e){var t="";return t+="."+c.cookieModal+" {\n            background-color: "+e["background-color-between-page-and-dialog"]+" !important;\n        }",(t+="."+c.modalContainer+" {\n            background-color: "+e["dialog-background-color"]+" !important;\n            border: 1px solid "+e["dialog-border-color"]+" !important;\n        }")+"."+c.closeModalIcon+" {\n            color: "+e["close-button-color"]+" !important;\n            background-color: "+e["dialog-background-color"]+" !important;\n        }"},e.buildSecondaryBtnStyle=function(e){var t="";return t+="."+c.secondaryButtonTheme+" {\n            border: "+e["secondary-button-border"]+" !important;\n            background-color: "+e["secondary-button-color"]+" !important;\n            color: "+e["secondary-button-text-color"]+" !important;\n        }",t+="."+c.secondaryButtonTheme+":enabled:hover {\n            color: "+e["secondary-button-text-color"]+" !important;\n            background-color: "+e["secondary-button-hover-color"]+" !important;\n            box-shadow: "+e["secondary-button-hover-shadow"]+" !important;\n            border: "+e["secondary-button-hover-border"]+" !important;\n        }",(t+="."+c.secondaryButtonTheme+":enabled:focus {\n            background-color: "+e["secondary-button-hover-color"]+" !important;\n            box-shadow: "+e["secondary-button-hover-shadow"]+" !important;\n            border: 2px solid "+e["secondary-button-focus-border-color"]+" !important;\n        }")+"."+c.secondaryButtonTheme+":disabled {\n            opacity: "+e["secondary-button-disabled-opacity"]+" !important;\n            color: "+e["secondary-button-disabled-text-color"]+" !important;\n            background-color: "+e["secondary-button-disabled-color"]+" !important;\n            border: "+e["secondary-button-disabled-border"]+" !important;\n        }"},e.buildPrimaryBtnStyle=function(e){var t="";return t+="."+c.primaryButtonTheme+" {\n            border: "+e["primary-button-border"]+" !important;\n            background-color: "+e["primary-button-color"]+" !important;\n            color: "+e["primary-button-text-color"]+" !important;\n        }",t+="."+c.primaryButtonTheme+":enabled:hover {\n            color: "+e["primary-button-text-color"]+" !important;\n            background-color: "+e["primary-button-hover-color"]+" !important;\n            box-shadow: "+e["primary-button-hover-shadow"]+" !important;\n            border: "+e["primary-button-hover-border"]+" !important;\n        }",(t+="."+c.primaryButtonTheme+":enabled:focus {\n            background-color: "+e["primary-button-hover-color"]+" !important;\n            box-shadow: "+e["primary-button-hover-shadow"]+" !important;\n            border: 2px solid "+e["primary-button-focus-border-color"]+" !important;\n        }")+"."+c.primaryButtonTheme+":disabled {\n            opacity: "+e["primary-button-disabled-opacity"]+" !important;\n            color: "+e["primary-button-disabled-text-color"]+" !important;\n            background-color: "+e["primary-button-disabled-color"]+" !important;\n            border: "+e["primary-button-disabled-border"]+" !important;\n        }"},e.buildRadioBtnStyle=function(e){var t="";return t+='input[type="radio"].'+c.cookieItemRadioBtn+" + label::before {\n            border: 1px solid "+e["radio-button-border-color"]+" !important;\n            background-color: "+e["dialog-background-color"]+" !important;\n        }",t+="."+c.cookieItemRadioBtnCtrlOutline+" {\n            outline: 2px solid "+e["radio-button-hover-background-color"]+" !important;\n        }",t+='input[type="radio"].'+c.cookieItemRadioBtn+":checked + label::after {\n            background-color: "+e["radio-button-checked-background-color"]+" !important;\n        }",t+='input[type="radio"].'+c.cookieItemRadioBtn+" + label:hover::before {\n            border: 1px solid "+e["radio-button-hover-border-color"]+" !important;\n        }",t+='input[type="radio"].'+c.cookieItemRadioBtn+" + label:hover::after {\n            background-color: "+e["radio-button-hover-background-color"]+" !important;\n        }",t+='input[type="radio"].'+c.cookieItemRadioBtn+" + label:focus::before {\n            border: 1px solid "+e["radio-button-hover-border-color"]+" !important;\n        }",(t+='input[type="radio"].'+c.cookieItemRadioBtn+" + label:focus::after {\n            background-color: "+e["radio-button-checked-background-color"]+" !important;\n        }")+'input[type="radio"].'+c.cookieItemRadioBtn+":disabled + label::before {\n            border: 1px solid "+e["radio-button-disabled-border-color"]+" !important;\n            background-color: "+e["radio-button-disabled-color"]+" !important;\n        }"},e}(),d=["ar","he","ps","ur","fa","pa","sd","tk","ug","yi","syr","ks-arab"],u={"close-button-color":"#666666","secondary-button-disabled-opacity":"1","secondary-button-hover-shadow":"0px 4px 10px rgba(0, 0, 0, 0.25)","primary-button-disabled-opacity":"1","primary-button-hover-border":"none","primary-button-disabled-border":"none","primary-button-hover-shadow":"0px 4px 10px rgba(0, 0, 0, 0.25)","banner-background-color":"#F2F2F2","dialog-background-color":"#FFFFFF","primary-button-color":"#0067B8","text-color":"#000000","secondary-button-color":"#EBEBEB","secondary-button-disabled-color":"rgba(0, 0, 0, 0.2)","secondary-button-border":"none","background-color-between-page-and-dialog":"rgba(255, 255, 255, 0.6)","dialog-border-color":"#0067B8","hyperlink-font-color":"#0067B8","secondary-button-hover-color":"#DBDBDB","secondary-button-hover-border":"none","secondary-button-disabled-border":"none","secondary-button-focus-border-color":"#000000","secondary-button-text-color":"#000000","secondary-button-disabled-text-color":"rgba(0, 0, 0, 0.2)","primary-button-hover-color":"#0067B8","primary-button-disabled-color":"rgba(0, 120, 215, 0.2)","primary-button-border":"none","primary-button-focus-border-color":"#000000","primary-button-text-color":"#FFFFFF","primary-button-disabled-text-color":"rgba(0, 0, 0, 0.2)","radio-button-border-color":"#000000","radio-button-checked-background-color":"#000000","radio-button-hover-border-color":"#0067B8","radio-button-hover-background-color":"rgba(0, 0, 0, 0.8)","radio-button-disabled-color":"rgba(0, 0, 0, 0.2)","radio-button-disabled-border-color":"rgba(0, 0, 0, 0.2)"},p={"close-button-color":"#E3E3E3","secondary-button-disabled-opacity":"0.5","secondary-button-hover-shadow":"none","primary-button-disabled-opacity":"0.5","primary-button-hover-border":"1px solid rgba(0, 0, 0, 0)","primary-button-disabled-border":"1px solid rgba(255, 255, 255, 0)","primary-button-hover-shadow":"none","banner-background-color":"#242424","dialog-background-color":"#171717","primary-button-color":"#4DB2FF","text-color":"#E3E3E3","secondary-button-color":"#171717","secondary-button-disabled-color":"#2E2E2E","secondary-button-border":"1px solid #C7C7C7","background-color-between-page-and-dialog":"rgba(23, 23, 23, 0.6)","dialog-border-color":"#4DB2FF","hyperlink-font-color":"#4DB2FF","secondary-button-hover-color":"#2E2E2E","secondary-button-hover-border":"1px solid #C7C7C7","secondary-button-disabled-border":"1px solid #242424","secondary-button-focus-border-color":"#C7C7C7","secondary-button-text-color":"#E3E3E3","secondary-button-disabled-text-color":"#E3E3E3","primary-button-hover-color":"#0091FF","primary-button-disabled-color":"#4DB2FF","primary-button-border":"1px solid #4DB2FF","primary-button-focus-border-color":"#4DB2FF","primary-button-text-color":"black","primary-button-disabled-text-color":"black","radio-button-border-color":"#E3E3E3","radio-button-checked-background-color":"#E3E3E3","radio-button-hover-border-color":"#4DB2FF","radio-button-hover-background-color":"rgba(227, 227, 227, 0.8)","radio-button-disabled-color":"rgba(227, 227, 227, 0.2)","radio-button-disabled-border-color":"rgba(227, 227, 227, 0.2)"},b={"close-button-color":"#E3E3E3","secondary-button-disabled-opacity":"0.5","secondary-button-hover-shadow":"none","primary-button-disabled-opacity":"0.5","primary-button-hover-border":"1px solid yellow","primary-button-disabled-border":"1px solid white","primary-button-hover-shadow":"none","banner-background-color":"black","dialog-background-color":"black","primary-button-color":"yellow","text-color":"white","secondary-button-color":"black","secondary-button-disabled-color":"black","secondary-button-border":"1px solid white","background-color-between-page-and-dialog":"rgba(0, 0, 0, 0.6)","dialog-border-color":"yellow","hyperlink-font-color":"yellow","secondary-button-hover-color":"black","secondary-button-hover-border":"1px solid yellow","secondary-button-disabled-border":"1px solid black","secondary-button-focus-border-color":"white","secondary-button-text-color":"white","secondary-button-disabled-text-color":"white","primary-button-hover-color":"#FFFF33","primary-button-disabled-color":"yellow","primary-button-border":"1px solid yellow","primary-button-focus-border-color":"yellow","primary-button-text-color":"black","primary-button-disabled-text-color":"black","radio-button-border-color":"white","radio-button-checked-background-color":"white","radio-button-hover-border-color":"yellow","radio-button-hover-background-color":"rgba(255, 255, 255, 0.8)","radio-button-disabled-color":"rgba(255, 255, 255, 0.2)","radio-button-disabled-border-color":"rgba(255, 255, 255, 0.2)"},m=function(){return(m=Object.assign||function(e){for(var t,o=1,n=arguments.length;o<n;o++)for(var r in t=arguments[o])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},h=n.locals,f=function(){function e(e,t,o,i,a){for(var l in this.containerElement=null,this.themes={light:m({},u),dark:m({},p),"high-contrast":m({},b)},this.preferencesCtrl=null,this.direction="ltr",this.isDirty={changed:!1},this.defaultCookieCategories=[{id:"c0",name:"1. Essential cookies",descHtml:"We use this cookie, read more <a href='link'>here</a>.",isUnswitchable:!0},{id:"c1",name:"2. Performance & analytics",descHtml:"We use this cookie, read more <a href='link'>here</a>."},{id:"c2",name:"3. Advertising/Marketing",descHtml:"Blah"},{id:"c3",name:"4. Targeting/personalization",descHtml:"Blah"}],this.defaultTextResources={bannerMessageHtml:"We use optional cookies to provide... read <a href='link'>here</a>.",acceptAllLabel:"Accept all",rejectAllLabel:"Reject all",moreInfoLabel:"More info",preferencesDialogCloseLabel:"Close",preferencesDialogTitle:"Manage cookie preferences",preferencesDialogDescHtml:"Most Microsoft sites...",acceptLabel:"Accept",rejectLabel:"Reject",saveLabel:"Save changes",resetLabel:"Reset all"},this.setContainerElement(e),(null==a?void 0:a.stylesNonce)?r(n,{attributes:{id:"ms-consent-banner-main-styles",nonce:a.stylesNonce}}):r(n,{attributes:{id:"ms-consent-banner-main-styles"}}),this.culture=t,this.onPreferencesChanged=o,this.cookieCategories=i||this.defaultCookieCategories,this.textResources=this.defaultTextResources,(null==a?void 0:a.textResources)&&this.setTextResources(a.textResources),null==a?void 0:a.themes){var c=l;(null==a?void 0:a.themes[c])&&this.createTheme(l,null==a?void 0:a.themes[c])}s.createThemeStyle(null==a?void 0:a.stylesNonce),(null==a?void 0:a.initialTheme)&&this.applyTheme(a.initialTheme),this.setDirection()}return e.prototype.setTextResources=function(e){for(var t=0,o=Object.keys(this.textResources);t<o.length;t++){var n=o[t];e[n]&&(this.textResources[n]=e[n])}},e.prototype.createTheme=function(e,t){var o=e;this.themes[o]=t,s.createTheme(this.themes[o],t)},e.prototype.applyTheme=function(e){if(!this.themes[e])throw new Error("Theme not found error");var t=this.themes[e];s.applyTheme(t)},e.prototype.showBanner=function(e){var t,o=this;if(!document.querySelector('meta[name="viewport"]')){var n=document.createElement("meta");n.name="viewport",n.content="width=device-width, initial-scale=1.0",document.head.appendChild(n)}this.hideBanner();var r='\n        <div class="'+h.bannerInform+'">\n            <span class="'+h.infoIcon+" "+h.textColorTheme+"\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" x='0px' y='0px' viewBox='0 0 44 44' width='24px' height='24px' fill='none' stroke='currentColor'>\n          <circle cx='22' cy='22' r='20' stroke-width='2'></circle>\n          <line x1='22' x2='22' y1='18' y2='33' stroke-width='3'></line>\n          <line x1='22' x2='22' y1='12' y2='15' stroke-width='3'></line>\n        </svg>\n        </span> \x3c!--  used for icon  --\x3e\n            <p class=\""+h.bannerInformBody+" "+h.hyperLinkTheme+" "+h.textColorTheme+'">\n                '+this.textResources.bannerMessageHtml+'\n            </p>\n        </div>\n\n        <div class="'+h.buttonGroup+'">\n            <button type="button" class="'+h.bannerButton+" "+h.secondaryButtonTheme+'">'+i.escapeHtml(this.textResources.acceptAllLabel)+'</button>\n            <button type="button" class="'+h.bannerButton+" "+h.secondaryButtonTheme+'">'+i.escapeHtml(this.textResources.rejectAllLabel)+'</button>\n            <button type="button" class="'+h.bannerButton+" "+h.secondaryButtonTheme+'">'+i.escapeHtml(this.textResources.moreInfoLabel)+"</button>\n        </div>\n        ",a=document.createElement("div");a.setAttribute("id","wcpConsentBannerCtrl"),a.setAttribute("class",h.bannerBody),a.setAttribute("dir",this.direction),a.setAttribute("role","alert"),a.innerHTML=r,null===(t=this.containerElement)||void 0===t||t.appendChild(a);var l=document.getElementsByClassName(h.bannerButton)[2];null==l||l.addEventListener("click",(function(){return o.showPreferences(e)}));var c=document.getElementsByClassName(h.bannerButton)[0];null==c||c.addEventListener("click",(function(){return o.onAcceptAllClicked(e)}));var s=document.getElementsByClassName(h.bannerButton)[1];null==s||s.addEventListener("click",(function(){return o.onRejectAllClicked(e)}))},e.prototype.hideBanner=function(){var e;if(document.getElementsByClassName(h.bannerBody)){for(var t=0,o=[].slice.call(document.getElementsByClassName(h.bannerBody));t<o.length;t++){var n=o[t];null===(e=this.containerElement)||void 0===e||e.removeChild(n)}this.hidePreferences()}},e.prototype.showPreferences=function(e){var t;this.preferencesCtrl||this.initPreferencesCtrl(e),null===(t=this.preferencesCtrl)||void 0===t||t.onPreferencesDialogShowing()},e.prototype.hidePreferences=function(){this.preferencesCtrl&&this.preferencesCtrl.hidePreferencesDialog()},e.prototype.initPreferencesCtrl=function(e){var t=this;this.preferencesCtrl=new l(this.cookieCategories,this.textResources,e,this.containerElement,this.direction,this.isDirty,(function(){return t.onPreferencesClosed()})),this.preferencesCtrl.createPreferencesDialog(),this.preferencesCtrl.addSaveButtonEvent((function(){return t.onPreferencesChanged(e)}))},e.prototype.onAcceptAllClicked=function(e){for(var t=0,o=this.cookieCategories;t<o.length;t++){var n=o[t];n.isUnswitchable||(e[n.id]=!0)}this.onPreferencesChanged(e)},e.prototype.onRejectAllClicked=function(e){for(var t=0,o=this.cookieCategories;t<o.length;t++){var n=o[t];n.isUnswitchable||(e[n.id]=!1)}this.onPreferencesChanged(e)},e.prototype.onPreferencesClosed=function(){this.preferencesCtrl=null},e.prototype.setContainerElement=function(e){if(e instanceof Element?this.containerElement=e:e&&e.length>0?this.containerElement=document.querySelector("#"+e):this.containerElement=null,!this.containerElement)throw new Error("Container not found error")},e.prototype.getContainerElement=function(){return this.containerElement},e.prototype.setDirection=function(e){if(e)this.direction=e;else{var t=this.culture.toLowerCase().split("-"),o=t[0];document.dir?this.direction=document.dir:document.body.dir?this.direction=document.body.dir:-1!==d.indexOf(o)||-1!==d.indexOf(t[0]+"-"+t[1])?this.direction="rtl":this.direction="ltr"}},e.prototype.getDirection=function(){return this.direction},e}()}])}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,o),i.exports}o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var n={};!function(){"use strict";function e(e){for(var t=Object.keys(e),o=t.length,n=new Array(o);o--;)n[o]=[t[o],e[t[o]]];return n}var t,r,i,a,l,c,s;o.d(n,{default:function(){return v}}),function(e){e[e.NoSelection=0]="NoSelection",e[e.Rejected=1]="Rejected",e[e.Accepted=2]="Accepted"}(t||(t={})),(s=r||(r={})).Required="c0",s.Analytics="c1",s.SocialMedia="c2",s.Advertising="c3",function(e){e.Required="Required",e.Analytics="Analytics",e.SocialMedia="SocialMedia",e.Advertising="Advertising"}(i||(i={})),(c=a||(a={})).msccCookie="MSCC",c.consentId="cid",function(e){e.light="light",e.dark="dark",e.highContrast="high-contrast"}(l||(l={}));var d="=";function u(){var t,o,n=null===(o=p(a.msccCookie))||void 0===o?void 0:o.split("-"),i=((t={})[r.Required]=!0,t),l=e(r);return null==n||n.forEach((function(e){var t=(e=e.trim()).split(d);l.forEach((function(e){e[1]!==t[0]||(i[e[1]]="2"===t[1]||"1"!==t[1]&&void 0)}))})),i[r.Required]||l.forEach((function(e){e[1]===r.Required?i[e[1]]=!0:i[e[1]]=void 0})),i}function p(e){var t,o=("; "+document.cookie).split("; "+e+"=");return 2===o.length?null===(t=o.pop())||void 0===t?void 0:t.split(";").shift():""}var b=function(){function e(){}return e.codes=["bg-BG","ro-MD","kk-KZ","nn-NO","mt-MT","lb-LU","id-ID","bs-BA","gd-GB","ga-IE","cy-GB","ca-ES","ar-SA","hu-HU","zh-HK","en-US","fr-FR","zh-CN","zh-TW","hr-HR","cs-CZ","da-DK","nl-NL","en-GB","vi-VN","et-EE","fi-FI","fr-CA","fr-FR","de-DE","el-GR","he-IL","hu-HU","it-IT","ja-JP","ko-KR","lv-LV","lt-LT","nb-NO","ms-MY","pl-PL","pt-BR","pt-PT","ro-RO","ru-RU","sr-Latn-CS","sr-LATN-RS","sk-SK","sl-SI","es-MX","es-ES","sv-SE","th-TH","tr-TR","uk-UA","hi-IN","is-IS"],e}(),m=(o(229),function(){function e(e){var t=this;this.apiEndPoint="",this.http=new XMLHttpRequest,this.retriesLeft=3,this.RecordConsent=function(e){t.callAPI("post",JSON.stringify(e),t.apiEndPoint+"/consent")},this.apiEndPoint=e}return e.prototype.callAPI=function(e,t,o){var n=this;this.http.onload=function(){n.http.status<=300&&n.http.status>=200?n.retriesLeft=3:(n.retriesLeft--,n.retriesLeft>0?n.callAPI(e,t,o):n.retriesLeft=3)},this.http.onerror=function(){n.retriesLeft--,n.retriesLeft>0?n.callAPI(e,t,o):n.retriesLeft=3},this.http.open(e,o),this.http.send(t)},e}()),h=function(){function e(){this.callbackArray=[],this.initCallbackArray=[]}return e.prototype.callRegisteredCallbacks=function(e){this.callbackArray.forEach((function(t){t(e)}))},e.prototype.registerCallback=function(e){this.callbackArray.push(e)},e.prototype.registerInitCallback=function(e,t,o){t&&o&&setTimeout((function(){e(o)}),0),this.initCallbackArray.push(e)},e.prototype.callRegisteredOninitCallbacks=function(e){this.initCallbackArray.forEach((function(t){t(e)}))},e}(),f=function(){function o(e,t){this.consentChangedCallbacks=new h,this.isConsentRequired=e,this.consentControl=t||void 0}return o.prototype.getConsent=function(){var o={Required:!0,Analytics:!1,SocialMedia:!1,Advertising:!1},n=p(a.msccCookie);return this.isConsentRequired||"NR"!==n?null==n||n.split("-").forEach((function(n){var a=n.trim().split(d),l=a[0],c=a[1],s=function(t){t=null==t?void 0:t.trim().toLowerCase();for(var o=0,n=e(r);o<n.length;o++){var a=n[o],l=a[0];if(a[1].toLowerCase()===t)return i[l]}}(l);s&&(o[s]=c===t.Accepted.toString())})):Object.keys(o).forEach((function(e){o[e]=!0})),o[i.Required]=!0,o},o.prototype.getConsentFor=function(e){if(!Object.values(i).includes(e))throw new Error(e+" is not a recognized consent category");return this.getConsent()[e]},o.prototype.manageConsent=function(){var e,t=u();null===(e=this.consentControl)||void 0===e||e.showPreferences(t)},o.prototype.onConsentChanged=function(e){this.consentChangedCallbacks.registerCallback(e)},o.prototype.fireConsentChanged=function(){this.consentChangedCallbacks.callRegisteredCallbacks(this.getConsent())},o}(),g=function(){function e(){}return e.on=function(e,t){this.events[e]?this.events[e].push(t):this.events[e]=[t]},e.setConsent=function(){console.warn("Consent not set - this method is not supported, setting consent is handled by library")},e.hasConsent=function(){return!0},e.isVisible=function(){return!!document.getElementById("wcpConsentBannerCtrl")},e.emit=function(e){for(var t=[],o=1;o<arguments.length;o++)t[o-1]=arguments[o];var n=this.events[e];n&&n.forEach((function(e){e.apply(null,t)}))},e.prototype.addEventListener=function(e,t,o){e.addEventListener?e.addEventListener(t,o):e.attachEvent("on"+t,(function(){o.call(e)}))},e.cookieName="MSCC",e.version="2.0.0",e.interactiveConsentEnabled=!1,e.eventTypes="consent",e.events={},e}(),y=g;window.mscc=y;var v=function(){function o(){}return o.init=function(e,t,n,r,i,a){var l="string"==typeof t?document.getElementById(t):t;if(l){var c=e.toLowerCase(),s=b.codes.filter((function(e){return function(e,t){var o=(e=e.toLowerCase())===(t=t.toLowerCase());if(!o){var n=t.split("-")[0];o=e.split("-")[0]===n}return o}(e,c)}));s&&0===s.length&&(e="en-US"),o.placeholderElement=l,r&&o.consentChangedCallbacks.registerCallback(r),o.saveCookie(),o.siteConsent=new f(!1),null==n||n(void 0,o.siteConsent),o.isInitReady=!0,this.consentChangedCallbacks.callRegisteredOninitCallbacks(o.siteConsent.getConsent())}else null==n||n(new Error("Invalid placeholder ID or element"))},o.onPreferencesChanged=function(n){var i,a=Object.keys(n).map((function(e){return e.toLowerCase()}));""===o.wcpCookie.consentId&&(o.wcpCookie.consentId=function(){for(var e="",t=0;t<8;++t)e+=(65536*(1+Math.random())|0).toString(36).substring(1);return e.toLowerCase()}()),e(r).forEach((function(e){(function(e,t){var o=e.length||0;if(0===o)return!1;for(var n,r=0;r<o;){if(n=e[r],t.toLowerCase()===n.toLowerCase()||t!=t&&n!=n)return!0;r++}return!1})(a,e[1].toLowerCase())&&(o.wcpCookie.categories[e[0]]=!0===n[e[1]]?t.Accepted:!1===n[e[1]]?t.Rejected:t.NoSelection)})),o.consentControl.hidePreferences(),o.consentControl.hideBanner(),o.saveCookie(!0),o.consentChangedCallbacks.callRegisteredCallbacks(o.siteConsent.getConsent()),o.siteConsent.fireConsentChanged(),!((i=function(e){for(var t=Object.keys(e),o=t.length,n=new Array(o);o--;)n[o]=e[t[o]];return n}(u())).indexOf(!1)>=0||i.indexOf(void 0)>=0||Object.keys(r).length!==i.length)&&y.emit("consent")},o.onConsentChanged=function(e){o.consentChangedCallbacks.registerCallback(e)},o.onInitCallback=function(e){var t;this.consentChangedCallbacks.registerInitCallback(e,o.isInitReady,null===(t=o.siteConsent)||void 0===t?void 0:t.getConsent())},o.getValidTheme=function(e){return e===l.light||e===l.dark||e===l.highContrast?e:l.light},o.saveCookie=function(t){void 0===t&&(t=!1);var n={consentId:"",consentStatus:""};if(t){var i=""+a.consentId+d+o.wcpCookie.consentId;n.consentId=o.wcpCookie.consentId,e(r).forEach((function(e){e[1]!==r.Required&&(i+="-"+e[1]+d+o.wcpCookie.categories[e[0]],n.consentStatus+="-"+e[0]+d+(2===o.wcpCookie.categories[e[0]]))})),o.setCookieOnRootDomain(a.msccCookie,i),n.consentStatus=n.consentStatus.substring(1),o.consentLogService.RecordConsent(n)}else o.setCookieOnRootDomain(a.msccCookie,"NR")},o.setCookieOnRootDomain=function(e,t){var n=window.location.hostname.split("."),r=n.pop(),i=e+"="+t+";path=/";for(o.isSameSiteSupported()&&(i+=";samesite=none;secure"),0===n.length&&(document.cookie=i+";domain="+r+";expires="+o.getCookieExpiryTime());p(a.msccCookie)!==t&&0!==n.length;)r=n.pop()+"."+r,document.cookie=i+";domain=."+r+";expires="+o.getCookieExpiryTime()},o.getCookieExpiryTime=function(){var e=new Date;return new Date(e.setDate(e.getDate()+1)).toUTCString()},o.isSameSiteSupported=function(){if(!/https/i.test(window.location.protocol))return!1;var e,t=navigator.userAgent;return!(/\(iP.+; CPU .*OS 12[_\d]*.*\) AppleWebKit\//.test(t)||(/\(Macintosh;.*Mac OS X 10_14[_\d]*.*\) AppleWebKit\//.test(t)?/Version\/.* Safari\//.test(t)&&!/Chrom(e|ium)/.test(t)||/^Mozilla\/[\.\d]+ \(Macintosh;.*Mac OS X [_\d]+\) AppleWebKit\/[\.\d]+ \(KHTML, like Gecko\)$/.test(t):/UCBrowser\/(\d+)\.(\d+)\.(\d+)[\.\d]* /.test(t)?(e=/UCBrowser\/(\d+)\.(\d+)\.(\d+)[\.\d]* /.exec(t))&&1e4*(1e4*parseInt(e[1])+parseInt(e[2]))+parseInt(e[3])<1200130002:/Chrom(e|ium)[^ \/]*\/(\d+)[\.\d]* /.test(t)&&(e=/Chrom(e|ium)[^ \/]*\/(\d+)[\.\d]* /.exec(t))&&parseInt(e[2])>=51&&parseInt(e[2])<=66))},o.consentCategories=i,o.themes=l,o.consentLogService=new m("https://consentreceiverfd-prod.azurefd.net/v1"),o.consentChangedCallbacks=new h,o.isInitReady=!1,o.localizedJson="NA",o}()}(),WcpConsent=n.default}();