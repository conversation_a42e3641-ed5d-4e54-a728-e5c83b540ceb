{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\133.0.6943.99\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\133.0.6943.99\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "neajdppkdcdipfabeoofebfddakdcjhd": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["tts_extension.js"]}, "description": "Component extension providing speech via the Google network text-to-speech service.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GSbNUMGygqQTNDMFGIjZNcwXsHLzkNkHjWbuY37PbNdSDZ4VqlVjzbWqODSe+MjELdv5Keb51IdytnoGYXBMyqKmWpUrg+RnKvQ5ibWr4MW9pyIceOIdp9GrzC1WZGgTmZismYR3AjaIpufZ7xDdQQv+XrghPWCkdVqLN+qZDA1HU+DURznkMICiDDSH2sU0egm9UbWfS218bZqzKeQDiC3OnTPlaxcbJtKUuupIm5knjze3Wo9Ae9poTDMzKgchg0VlFCv3uqox+wlD8sjXBoyBCCK9HpImdVAF1a7jpdgiUHpPeV/26oYzM9/grltwNR3bzECQgSpyXp0eyoegwIDAQAB", "manifest_version": 2, "name": "Google Network Speech", "permissions": ["metricsPrivate", "systemPrivate", "ttsEngine", "https://www.google.com/"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\133.0.6943.99\\resources\\network_speech_synthesis", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.49\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "google": {"services": {"account_id": "", "last_signed_in_username": "<EMAIL>"}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "F21563D653EC511AEA01A24313A925FE8776992C6590630D01B98E7C0A0D19F5"}, "extensions": {"ui": {"developer_mode": "AD9AD2352697BDDE6B21EF19F67A0F3607326846639626EA0972A4B130ED3DAE"}}, "homepage": "F8D603718731D4D82656D6B568D5C3E47A6B9738605B1B93B4E61F3DB7619AB9", "homepage_is_newtabpage": "D75F59A45ECBD01646F75C1FC60D13F353E1A890FECC1607103A5B7BFB83FC0E", "session": {"restore_on_startup": "F5E2D25EBE497F5217F6561F804267B90116BAE2CC8F7A85FF7BFC24048441E3", "startup_urls": "D9772D30A862CC80B47594A14C43BA6FDDF7F452BF303AB7B865E6DE4EC113F3"}}, "browser": {"show_home_button": "378D9D477090EF381DE6D9D29470E327922E93B7C4B888B2CF8AAD2698B793BE"}, "default_search_provider_data": {"template_url_data": "F85F3EFAB566D8BBF2752E6F544EF17354DAE63C9E1DC09E334E733D7B2A9B63"}, "enterprise_signin": {"policy_recovery_token": "E6C4F27B97BC29F459A203320C0A4390800E582E23B54BFF1802C5A6E91A7372"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "98112EC1E1884A616BE6E8A2F3099A7C35DDB11B3F4C3893EDA1C5A21720A561", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "0993686593032C3ED1D826B561C1771AA792B2C55666FB3C763AFF4C565FFE2D", "neajdppkdcdipfabeoofebfddakdcjhd": "65F903D3917751C741F6C542B60F4481C2836D1DD7D275108B4B89E9C0C6C56C", "nkeimhogjdpnpccoofpliimaahmaaome": "A7D9BFD0ED8D745BFA131D0E181DA458E8528357A9C0DB3255CD88E6583A9FE2"}, "ui": {"developer_mode": "F0B1ECF68B66B0CE2046B6D838174803C07BA02FBC5A6D3C3094F752B271E012"}}, "google": {"services": {"account_id": "0E8AC9A2761F689FAA0159F768F67E9CB9B231F230FA4B300DA1C8BF2A443372", "last_signed_in_username": "89314EE178598DF57CEF18687A1B22FBF5B5D2C347E8A5F713703AC543BA1C65", "last_username": "021B829D13B7B4A46493A79FDC1AA3204C21B9F23200BC1B1326BD2BB296B440"}}, "homepage": "BCDA4B9519EB75B180DFE46EC6DF1D23946D8EBC22D8FB147831AF3A47495538", "homepage_is_newtabpage": "692493F9549D0E4EE31A0B9616192E2B7DAE4FE48DE8C04649B3A3B78B31A59C", "media": {"cdm": {"origin_data": "15DA6E84AC40C23B6D5DA3ECA3E2F68EA244C1B34E81348A7445FADBE42D94AD"}, "storage_id_salt": "601B322BF810D73C4B5C14EB80BB3FF2CBFE3A9DECE6179281729780712D10FF"}, "module_blocklist_cache_md5_digest": "78DAD595E20060346A803A020809D1C90BEB4ECAB5BF7B83CCD7D6F7BB8E16D9", "pinned_tabs": "BC3A01E15989CB3EB36D1846495F559C87D9A9B5801FB5E8D397D0B04508EF77", "prefs": {"preference_reset_time": "E33397E167358CAF12F8B1ECE97D17B5C3C86C2E35D23AFC0D8E0DD36FA424B0"}, "safebrowsing": {"incidents_sent": "CCD5E2B5A77AD57C56218463A84F26CB0310CEC09629BD977519F3983EE22314"}, "search_provider_overrides": "A9F0EE75292ACDB9F985C64E26636CE0B47D24976507D6CF25A2748495DFAE1F", "session": {"restore_on_startup": "3D1035EE4A459B489869C3C6EF2BA09008CF27A0F4DDAF2C4CE59F98DB15EA33", "startup_urls": "F19ACA07000FAEE5379D6FD1241CC69C1CB8811FBFCF8B702D0D7C6136B6EC63"}}, "super_mac": "429526877053BC858B1A5B2EE4BEAA7BC3F2CB01FB83AE88F2DE6DDE6ACD4DEF"}}